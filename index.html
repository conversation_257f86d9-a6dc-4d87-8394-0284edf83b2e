<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Life Farm - Đ<PERSON><PERSON> F&B</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #f8fdf8;
        }
        .ytp-title-channel-logo, .ytp-title-channel {
            display: none !important;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            font-weight: 700;
            text-align: center;
        }
        .section-subtitle {
            text-align: center;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #27ae60;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }
        .hero h2 {
            font-size: 1.8rem;
            margin-bottom: 25px;
            color: #34495e;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }
        .hero-description {
            font-size: 1.3rem;
            margin-bottom: 40px;
            color: #5a6c7d;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.7;
            position: relative;
            z-index: 1;
        }
        .hero {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-logo {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            margin: 0 auto 30px;
            display: block;
            object-fit: cover;
            box-shadow: 0 20px 40px rgba(40, 167, 69, 0.3), 
                        0 15px 25px rgba(40, 167, 69, 0.2),
                        0 5px 10px rgba(0, 0, 0, 0.1);
            border: 5px solid rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        .hero-logo:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(40, 167, 69, 0.4), 
                        0 20px 30px rgba(40, 167, 69, 0.3),
                        0 10px 15px rgba(0, 0, 0, 0.15);
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .hero h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        .hero-video {
            margin: 30px 0;
            max-width: 900px;
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }
        .hero-video iframe {
            width: 100%;
            height: 450px;
            border-radius: 15px;
            border: none;
        }
        .cta-button {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 35px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }
        .problem-statement, .benefits, .value-proposition, .final-cta {
            padding: 80px 0;
            position: relative;
        }
        .problem-statement {
            background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
            border-top: 1px solid rgba(52, 152, 219, 0.1);
        }
        .benefits {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            position: relative;
        }

        .value-proposition {
            background: linear-gradient(135deg, #fefefe 0%, #f5f9fa 100%);
            border-top: 1px solid rgba(149, 165, 166, 0.1);
            border-bottom: 1px solid rgba(149, 165, 166, 0.1);
        }
        .final-cta {
            background: linear-gradient(135deg, #e8f8f5 0%, #d5f3e3 100%);
            position: relative;
        }
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(276px, 1fr));
            gap: 30px;
            position: relative;
            z-index: 1;
        }
        .benefit-card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 20px;
            padding: 35px 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 
                        0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .benefit-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(39, 174, 96, 0.2), 
                        0 10px 25px rgba(0, 0, 0, 0.1);
        }
            .benefit-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: inline-block;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-weight: bold;
        }
        .benefit-icon.security {
            background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
        }
        .benefit-icon.control {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .benefit-icon.brand {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }
        .benefit-icon.legal {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        }
        .benefit-icon.family {
            background: linear-gradient(135deg, #f03 0%, #f52aa0 100%);
        }
        .btn-primary {
            background-color: #28a745;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #218838;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }
        .cta-buttons {
            margin: 20px 0;
        }
        .contact-options {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        .contact-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        .contact-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        .contact-btn.phone {
            background-color: #28a745;
        }
        .contact-btn.phone:hover {
            background-color: #218838;
        }
        .contact-btn.email {
            background-color: #dc3545;
        }
        .contact-btn.email:hover {
            background-color: #c82333;
        }
        .contact-btn.facebook {
            background-color: #1877f2;
        }
        .contact-btn.facebook:hover {
            background-color: #166fe5;
        }
        .contact-btn.address {
            background-color: #17a2b8;
            flex-basis: 100%;
            justify-content: center;
        }
        .contact-btn.address:hover {
            background-color: #138496;
        }
        .modern-icon {
            font-style: normal;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            margin-right: 8px;
            font-size: 21px;
        }
        .hashtag {
            margin-top: 10px;
            font-style: italic;
        }
        .footer-note {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            padding: 60px 0;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .certification-info {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        .certification-info h4 {
            color: #27ae60;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        .certification-info p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #ecf0f1;
        }
        .products {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8fdfc 0%, #e1f5fe 100%);
            position: relative;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(267px, 1fr));
            gap: 25px;
            margin-top: 50px;
            position: relative;
            z-index: 1;
        }
        .product-item {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(39, 174, 96, 0.1);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(10px);
        }
        .product-item > p{
            font-size: 15px;
        }
        .product-item:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 15px 40px rgba(39, 174, 96, 0.2);
            border-color: #27ae60;
        }
        .product-image {
            width: 164px;
            height: 164px;
            margin: 0 auto 7px;
            background-size: cover;
            background-position: center;
            border-radius: 15px;
            border: 3px solid #e8f5e8;
        }
        .product-rau-la { background-image: url('/rau-la2.jpg'); }
        .product-nam { background-image: url('/nam.jpg'); }
        .product-mang { background-image: url('/mang-tay.webp'); }
        .product-trai-cay { background-image: url('/trai-cay.webp'); }
        .product-gia-vi { background-image: url('/gia-vi.jpg'); }
        
        /* Responsive video */
        @media (max-width: 768px) {
            .hero {
                padding: 60px 0;
            }
            .hero h1 {
                font-size: 2.2rem;
            }
            .hero h2 {
                font-size: 1.4rem;
            }
            .hero-logo {
                width: 140px;
                height: 140px;
                margin-bottom: 20px;
            }
            .hero-video {
                max-width: 100%;
                margin: 20px 0;
            }
            .hero-video iframe {
                height: 250px;
                border-radius: 10px;
            }
            .problem-statement, .benefits, .value-proposition, .final-cta, .products {
                padding: 50px 0;
            }
            .benefit-card {
                padding: 25px 20px;
            }
        }
        
        @media (max-width: 480px) {
            .hero h1 {
                font-size: 1.8rem;
            }
            .hero h2 {
                font-size: 1.2rem;
            }
            .hero-logo {
                width: 120px;
                height: 120px;
            }
            .hero-video iframe {
                height: 200px;
            }
            .problem-statement, .benefits, .value-proposition, .final-cta, .products {
                padding: 40px 0;
            }
        }
        
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <!-- Logo Life Farm -->
            <img src="https://lifefarm.remagan.com/NONG_SAN_LOGO.png" alt="Life Farm Logo" class="hero-logo">
            
            <h1>LIFE FARM</h1>
            <h2>TỰ TIN LÀ ĐỐI TÁC KHÔNG THỂ THIẾU CHO NGÀNH F&B</h2>
            <p class="hero-description">
                Trong một thị trường F&B ngày càng cạnh tranh khốc liệt, thành công không chỉ nằm ở hương vị món ăn mà còn ở sự minh bạch và an toàn tuyệt đối trong từng nguyên liệu.
            </p>
            
            <!-- Video YouTube -->
            <div class="hero-video">
                <iframe src="https://www.youtube.com/embed/VmbyrLaVbZA?modestbranding=1&rel=0&showinfo=0&iv_load_policy=3&cc_load_policy=0&controls=1&disablekb=1&fs=1&autohide=1" 
                        title="Life Farm - Giới thiệu sản phẩm" 
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                        allowfullscreen>
                </iframe>
            </div>
            
            <button class="cta-button">Tìm hiểu thêm</button>
        </div>
    </section>
    <section class="the-start">
        <div class="container">
            <h3>Tại sao L’ife Farm lại ra đời?</h3>
            <p>Chúng tôi tin rằng bữa ăn ngon bắt đầu từ nguyên liệu tươi mỗi ngày. L’ife Farm được thành lập với sứ mệnh mang rau củ quả tươi – Nguồn gốc minh bạch – An toàn cho sức khỏe – Tiện lợi đến tận tay người tiêu dùng, giúp bạn không phải tích trữ nhiều ngày mà vẫn luôn có thực phẩm mới, giàu dinh dưỡng cho gia đình.
Với đội ngũ chuyên nghiệp, nguồn hàng phong phú và khung đặt hàng linh hoạt, chúng tôi mang đến sự tiện lợi tối đa, để bạn chỉ cần chọn – còn lại hãy để L’ife Farm lo. 
</p>
        </div>
    </section>
    <!-- Problem Statement -->
    <section class="problem-statement">
        <div class="container" >
            <h3>Thách thức hiện nay của người tiêu dùng đi mua hàng</h3>
            <ol style="
                width: 550px;
                margin: 0 auto;
                text-align: center;
            ">
                <li>Khó phân biệt chất lượng thật – giả</li>
                <li>Lo ngại về an toàn thực phẩm</li>
                <li>Hạn chế về thời gian</li>
                <li>Khó bảo quản tại nhà</li>
                <li>Giá cả biến động</li>
            </ol>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits">
        <div class="container">
            <h3>Lợi ích khi chọn Life Farm</h3>
            <p class="section-subtitle">Lựa chọn Life Farm – Lựa chọn an tâm, tiện lợi xanh cho mỗi ngày bận rộn</p>
            
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon security">⚡</div>
                    <h4>Nguồn gốc minh bạch</h4>
                    <p>
                        Rau củ được trồng tại trang trại đạt chuẩn, áp dụng hệ thống truy xuất nguồn gốc rõ ràng, minh bạch từ hạt giống đến bàn ăn. Một số sản phẩm đã đạt chứng nhận VietGAP.
                    </p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon control">◈</div>
                    <h4>Tươi xanh & dinh dưỡng</h4>
                    <p>Thu hoạch và giao nhanh, giữ trọn hương vị tự nhiên và dưỡng chất thiết yếu.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon brand">★</div>
                    <h4>Tiện lợi tối đa</h4>
                    <p>
                        Sơ chế sẵn, sẵn sàng chế biến – giải pháp tiết kiệm thời gian cho người bận rộn (dân văn phòng, gia đình trẻ, người làm việc với lịch trình dày đặc….)
                    </p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon legal">◉</div>
                    <h4>An toàn cho sức khỏe</h4>
                    <p>
                        Không sử dụng hóa chất độc hại, không tồn dư thuốc trừ sâu.
                    </p>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon family">♥</div>
                    <h4>Gắn kết yêu thương</h4>
                    <p>
                        Giúp bạn có thêm thời gian tận hưởng khoảnh khắc bên gia đình thay vì tất bật trong bếp.
                    </p>
                </div>
            </div>
            <div class="hashtag">
                <strong> Với Life Farm, mỗi bữa cơm không chỉ ngon mà còn trọn vẹn an tâm và yêu thương</strong>
            </div>
        </div>
    </section>

    <!-- Value Proposition -->
    <section class="value-proposition">
        <div class="container">
            <h3 class="section-subtitle">Không chỉ bán rau củ – Life Farm đồng hành cùng bữa ăn của bạn</h3>
            <p>Tại Life Farm, chúng tôi hiểu rằng bạn không chỉ cần rau củ tươi ngon, sạch và an toàn, mà còn cần sự an tâm tuyệt đối để chăm sóc sức khỏe gia đình mỗi ngày.</p>
            <p>Chúng tôi không đơn thuần là nơi bán rau củ, mà là người bạn đồng hành – giúp bạn:</p>
            <ul>
                <li>Chọn được thực phẩm tươi xanh, giàu dinh dưỡng.</li>
                <li>Yên tâm về nguồn gốc minh bạch và an toàn vệ sinh thực phẩm.</li>
                <li>Tiết kiệm thời gian sơ chế, để bạn tận hưởng trọn vẹn khoảnh khắc bên gia đình.</li>
            </ul>   
            <div class = "hashtag">
                <strong>
                    Life Farm – Mang cả nông trại sạch đến gian bếp nhà bạn.
                </strong>   
            </div>

        </div>
    </section>

    <!-- Products Section -->
    <section class="products">
        <div class="container">
            <h3>Dòng sản phẩm đa dạng, chất lượng cao</h3>
            <p class="section-subtitle">Life Farm cung cấp đầy đủ các loại nguyên liệu tươi sạch cho ngành F&B</p>
            
            <div class="products-grid">
                <div class="product-item">
                    <div class="product-image product-rau-la"></div>
                    <h4>1.	Rau ăn sống, ăn gỏi</h4>
                    <p>-	Rau xà lách: giàu vitamin A, C, giúp đẹp da, hỗ trơ tiêu hóa, ít calo tốt cho giảm cân. </p>
                    <p>-	Rau diếp cá: thanh nhiệt, giải độc, hỗ trợ trị mụn và táo bón</p>
                    <p>-	Rau húng quế: Kích thích tiêu hóa, kháng khuẩn, giúp giảm đầy hơi</p>
                    <p>-	Rau húng lủi (húng bạc hà): giúp mát cơ thể, dễ tiêu, giảm buồn nôn</p>
                    <p>-	Tía tô: giải cảm, kháng viêm, tốt cho da, hỗ trợ hô hấp</p>
                    <p>-	Ngò rí (mùi ta): Thải độc kim loại nặng, hỗ trợ tiêu hóa, tăng hương vị món ăn</p>

                </div>
                
                <div class="product-item">
                    <div class="product-image product-nam"></div>
                    <h4>2.	Rau gia vị, ăn kèm món chính</h4>
                    <p> -	Hành lá: Giàu vitamin C, kháng khuẩn, hỗ trợ hô hấp.</p>
                    <p> -	Rau răm: Tốt cho tiêu hóa, ấm bụng, kháng khuẩn tự nhiên.</p>
                    <p> -	Kinh giới: Hỗ trợ giải cảm, giảm ho, chống viêm.</p>
                    <p> -	Ngò gai: Giúp tiêu hóa tốt, tăng hương thơm món ăn</p>
                    <p> -	Lá lốt: Giảm đau nhức xương khớp, hỗ trợ tiêu hóa.</p>

                </div>
                
                <div class="product-item">
                    <div class="product-image product-mang"></div>
                    <h4>3.	Rau lá nấu canh</h4>
                    <p>-	Rau muống: Giàu sắt, canxi, thanh nhiệt, lợi tiểu</p>
                    <p>-	Rau mồng tơi: Giàu chất xơ, vitamin A, C, giúp nhuận tràng</p>
                    <p>-	Rau ngót: Giàu vitamin A, C, canxi; thanh nhiệt, hỗ trợ mắt và da.</p>
                    <p>-	Rau dền: Giàu sắt, canxi; mát gan, thanh nhiệt</p>
                    <p>-	Cải xanh: Chống táo bón, giàu vitamin K và chất chống oxy hóa</p>
                    <p>-	Cải bó xôi: Nhiều sắt, folate, tốt cho máu và tim mạch</p>
                    <p>-	Cải cúc (tần ô): Tốt cho hệ tiêu hóa, giúp ngủ ngon.</p>
                    <p>-	Rau sam: Giàu omega-3 thực vật, giải độc, tốt cho tim mạch</p>

                </div>
                
                <div class="product-item">
                    <div class="product-image product-trai-cay"></div>
                    <h4>4.	Nấm </h4>
                    <p> - Nấm rơm: Giàu protein, tăng sức đề kháng</p>
                    <p> - Nấm đông trùng hạ thảo: Giàu cordycepin, adenosine, acid amin, vitamin B, nguyên tố vi lượng. Tăng sức đề kháng, cải thiện chức năng hô hấp, hỗ trợ tim mạch, chống mệt mỏi, bồi bổ cơ thể.</p>
                    <p> - Nấm kim châm: Giàu chất xơ, hỗ trợ tiêu hóa</p>
                    <p> - Nấm linh chi: Chứa triterpenoid, polysaccharide, germanium hữu cơ. Tăng miễn dịch, chống oxy hóa, hỗ trợ huyết áp, bảo vệ gan, ngăn ngừa một số bệnh mãn tính.</p>
                    <p> - Nấm hương (shiitake): Giàu vitamin D, B, khoáng chất (selen, đồng, kẽm) và chất xơ. Tăng cường miễn dịch, hỗ trợ tim mạch, giảm cholesterol xấu, tốt cho xương khớp.</p>
                    <p> - Nấm kim châm: Nhiều chất xơ, niacin, kali, lysine, acid folic. Hỗ trợ tiêu hóa, giúp giảm mỡ máu, tăng trí nhớ, phòng táo bón.</p>
                    <p> - Nấm bào ngư (nấm sò): Nhiều protein, chất xơ, vitamin B, ergothioneine – chất chống oxy hóa mạnh. Tăng miễn dịch, hỗ trợ gan, giảm đường huyết, tốt cho tim mạch.</p>

                </div>
                
                <div class="product-item">
                    <div class="product-image product-gia-vi"></div>
                    <h4>5.	Các loại củ quả</h4>
                    <p> -	Cà rốt: Giàu beta-carotene, vitamin A tốt cho mắt, da, hệ miễn dịch. Chống oxy hóa, hỗ trợ tim mạch.</p>
                    <p> -	Khoai tây: Cung cấp năng lượng nhanh, giàu vitamin C, kali. Giúp no lâu, hỗ trợ tiêu hóa, kiểm soát huyết áp.</p>
                    <p> -	Khoai lang: Giàu chất xơ, vitamin A, C, B6. Tốt cho tiêu hóa, tim mạch, giúp kiểm soát đường huyết.</p>
                    <p> -	Củ cải trắng: Thanh nhiệt, hỗ trợ tiêu hóa, giàu vitamin C. Giảm cholesterol, hỗ trợ thải độc cơ thể.</p>
                    <p> -	Củ cải đỏ (củ dền): Giàu sắt, folate, mangan. Hỗ trợ máu huyết, cải thiện sức bền, chống oxy hóa.</p>
                    <p> -	Củ hành tây. Chứa hợp chất lưu huỳnh, vitamin C, quercetin. Kháng viêm, tăng đề kháng, tốt cho tim mạch.</p>
                    <p> -	Su hào: Giàu vitamin C, chất xơ. Thanh nhiệt, tốt cho tim mạch, kiểm soát cân nặng.</p>
                    <p> -	Cà chua: Làm đẹp da, tăng sức đề kháng, bảo vệ tim mạch. Giàu lycopene (chất chống oxy hóa mạnh), vitamin C, K, kali. Giúp giảm nguy cơ ung thư, hỗ trợ thị lực, chống lão hóa.</p>
                    <p> -	Bí đỏ: Tốt cho mắt, tăng sức đề kháng, hỗ trợ tiêu hóa, giúp ngủ ngon. Giàu beta-carotene (tiền vitamin A), vitamin C, E, kali, chất xơ. Có tác dụng chống oxy hóa, bảo vệ tim mạch, tốt cho da và hệ miễn dịch.</p>

                </div>
                <div class="product-item">
                    <div class="product-image product-gia-vi"></div>
                    <h4>6.	Nguyên liệu gia vị </h4>
                    <p> -	Tỏi: Kháng khuẩn, tăng miễn dịch, giảm mỡ máu. Hỗ trợ huyết áp, phòng ngừa bệnh tim.</p>
                    <p> -	Gừng: Ấm bụng, chống buồn nôn, hỗ trợ tiêu hóa. Kháng viêm, tăng lưu thông máu.</p>
                    <p> -	Nghệ: Giàu curcumin chống viêm, chống oxy hóa. Tốt cho gan, dạ dày, khớp.</p>
                    <p> -	Củ sắn (mì):  Nguồn tinh bột dồi dào, giúp no lâu. Cung cấp năng lượng, giàu vitamin C.</p>
                    <p> -	Sả: Kháng khuẩn, thanh lọc cơ thể</p>
                    <p> -	Giá đỗ: Thanh mát, tốt cho tiêu hóa</p>
                    <p> -	Chanh: Giàu vitamin C, tăng miễn dịch</p>
                    <p> -	Trái sấu: Giàu vitamin C, axit hữu cơ và khoáng chất; giúp thanh nhiệt, hỗ trợ tiêu hóa, giảm ho, tăng sức đề kháng và kích thích ăn ngon.</p>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="final-cta">
        <div class="container">
            <h3>Chọn Life Farm, chọn sự minh bạch, an toàn và thành công</h3>
            <p  class="section-subtitle">Life Farm – Người bạn đồng hành tin cậy cho doanh nghiệp bạn</p>

            <!-- Contact Options -->
            <div class="contact-options">
                <a href="tel:0325392290" class="contact-btn phone">
                    <span class="modern-icon">☎</span> Hotline/zalo: 0325.392.290  
                </a>
                <a href="mailto:<EMAIL>" class="contact-btn email">
                    <span class="modern-icon">✉</span> <EMAIL>
                </a>
                <a href="https://www.facebook.com/profile.php?id=61558287840135" target="_blank" class="contact-btn facebook">
                    <span class="modern-icon">f</span> Life Farm Official
                </a>
                <a href="https://www.facebook.com/profile.php?id=61579408850947" target="_blank" class="contact-btn facebook">
                    <span class="modern-icon">f</span> Life Farm B2B
                </a>
                <a href="https://maps.app.goo.gl/EJehMsmHi6qGutdWA" target="_blank" class="contact-btn address">
                    <span class="modern-icon">⌘</span> Địa chỉ: 06 Tân An, P. Nam Nha Trang, T. Khánh Hoà
                </a>
            </div>
            
            <div class="hashtag">#Lifefarm</div>
        </div>
    </section>

    <!-- Footer Note -->
    <footer class="footer-note">
        <div class="container">
            <div class="certification-info">
                <h4>Chứng nhận VietGAP</h4>
                <p>
                    <strong>VietGAP</strong> là chứng nhận "Vietnamese Good Agricultural Practices" – tức là "Thực hành sản xuất nông nghiệp tốt tại Việt Nam", tiêu chuẩn về canh tác sạch, an toàn, thân thiện môi trường, được nhà nước kiểm định chặt chẽ.
                </p>
            </div>
        </div>
    </footer>
    
    <script>
        function downloadCatalogue() {
            // Bạn có thể thay đổi URL này thành link catalogue thật
            const catalogueUrl = '/catalogue/lifefarm-catalogue.pdf';
            
            // Tạo link download
            const link = document.createElement('a');
            link.href = catalogueUrl;
            link.download = 'Life-Farm-Catalogue.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Hoặc mở trong tab mới nếu file không tồn tại
            // window.open(catalogueUrl, '_blank');
            
            // Tạm thời alert cho demo
            alert('Catalogue đang được chuẩn bị. Vui lòng liên hệ trực tiếp để nhận catalogue!');
        }
        
        // Smooth scroll cho button "Tìm hiểu thêm"
        document.querySelector('.cta-button').addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.problem-statement').scrollIntoView({
                behavior: 'smooth'
            });
        });
        
        // Track clicks (có thể tích hợp Google Analytics)
        function trackContact(type) {
            console.log('Contact clicked:', type);
            // gtag('event', 'contact', { method: type });
        }
        
        // Add event listeners
        document.querySelectorAll('.contact-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const type = this.classList.contains('phone') ? 'phone' : 
                           this.classList.contains('email') ? 'email' : 
                           this.classList.contains('facebook') ? 'facebook' : 'address';
                trackContact(type);
            });
        });
    </script>
</body>
</html>

